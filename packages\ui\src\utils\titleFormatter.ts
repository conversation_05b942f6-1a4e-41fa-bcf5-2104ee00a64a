import { SubstituteVarData, substituteVars } from "@repo/app-db-utils/src/methods/object";
import { sanitizeTextForProcessing } from "@repo/app-db-utils/src/methods/string";
import { Database, Record } from "@ui/typings/database";
import { MyWorkspaceMember } from "@ui/typings/workspace";
import { recordValueToText, transformRawRecords } from "@repo/app-db-utils/src/utils/db";
import { ProcessedDbRecord } from "@repo/app-db-utils/src/typings/db";
import { membersToPersons } from "@ui/components/workspace/main/views/table/renderer/fields/person";


type RecordInput = Record | ProcessedDbRecord;


interface ExtendedRecord extends ProcessedDbRecord {
    titleFormat?: string;
    database?: Database;
}

export const formatRecordTitle = (record: ExtendedRecord, titleFormat: string, members?: MyWorkspaceMember[], database?: Database): string => {
    if (!record || !record.processedRecordValues || !titleFormat || typeof titleFormat !== 'string') {
        return "Untitled";
    }

    const vars: SubstituteVarData = {};

    // Use existing substituteVars pattern to extract column IDs
    const pattern = /\{\{([^}]+)\}\}/g;
    const titleFormatColumnIds = new Set<string>();
    let match: RegExpExecArray | null;
    while ((match = pattern.exec(titleFormat)) !== null) {
        titleFormatColumnIds.add(match[1].trim());
    }

    // Process each column ID found in the title format
    titleFormatColumnIds.forEach(columnId => {
        // Check if column still exists in database definition
        const columnExists = database?.definition?.columnsMap?.[columnId];

        if (!columnExists) {
            // Column was deleted - show "Untitled" instead of UUID
            vars[columnId] = 'Untitled';
            return;
        }

        // Get the processed value for existing columns
        const processedValue = record.processedRecordValues[columnId];

        // If the column exists in definition but not in processed values, show "Untitled"
        if (processedValue === undefined || processedValue === null) {
            vars[columnId] = 'Untitled';
            return;
        }

        let textValue = recordValueToText(processedValue);

        // Special handling for UUID values in title formats
        if (typeof textValue === 'string' && textValue.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
            textValue = `[${textValue}]`;
        }

        // Replace empty values with "Untitled" for each individual column
        if (!textValue || (typeof textValue === 'string' && textValue.trim() === '')) {
            textValue = 'Untitled';
        }

        vars[columnId] = textValue;
    });

    const result = substituteVars(titleFormat, vars, undefined, 'curly');
    const trimmedResult = result.trim();
    return trimmedResult || "Untitled";
};

export const getRecordTitle = (
    record: RecordInput, // Accept both 
    titleColId: string,
    defaultTitle: string,
    isContacts: boolean,
    database?: Database,
    members?: MyWorkspaceMember[]
): string => {
    if (!record) {
        return defaultTitle;
    }

    // Convert raw record to processed record if needed
    let processedRecord: ExtendedRecord;

    // Type guard to check if it's a ProcessedDbRecord
    if ('processedRecordValues' in record && record.processedRecordValues) {
        // Already a processed record
        processedRecord = record as ExtendedRecord;
    } else if ('recordValues' in record && record.recordValues && database && members) {
        // Raw record - convert to processed record
        const persons = membersToPersons(members);
        const processedRecords = transformRawRecords(database.definition, [record as any], persons);
        if (processedRecords.length > 0) {
            processedRecord = processedRecords[0] as ExtendedRecord;
        } else {
            return defaultTitle;
        }
    } else {
        // Raw record but no database/members - use simple fallback
        return getSimpleRecordTitle(record as any, titleColId, defaultTitle, isContacts);
    }

    // Use title format if available
    if (database?.definition?.titleFormat && typeof database.definition.titleFormat === 'string' && database.definition.titleFormat.trim()) {
        return formatRecordTitle(processedRecord, database.definition.titleFormat, members, database);
    }

    // Use single column title
    if (processedRecord.processedRecordValues[titleColId] !== undefined && processedRecord.processedRecordValues[titleColId] !== null) {
        let textValue = String(recordValueToText(processedRecord.processedRecordValues[titleColId])).trim();

        // Special handling for UUID columns
        if (textValue && textValue.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
            return `[${textValue}]`;
        }

        return textValue || 'Untitled';
    }

    return defaultTitle;
};

// Simple fallback for raw records without database/members
const getSimpleRecordTitle = (record: any, titleColId: string, defaultTitle: string, isContacts: boolean): string => {
    if (record.title) {
        return record.title;
    }

    if (isContacts && defaultTitle) {
        const lastName = String(record.recordValues?.['lastName'] || '');
        const firstName = String(record.recordValues?.['firstName'] || '');
        const fullName = `${lastName} ${firstName}`.trim();
        return fullName || 'Untitled';
    }

    if (record.recordValues?.[titleColId]) {
        const value = String(record.recordValues[titleColId]).trim();

        // Special handling for UUID values
        if (value && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
            return `[${value}]`;
        }

        return value || 'Untitled';
    }

    return defaultTitle;
};