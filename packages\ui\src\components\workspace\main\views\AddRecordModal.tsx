"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@ui/components/ui/dialog";
import { Button } from "@ui/components/ui/button";
import { useViews } from "@ui/providers/views";
import { useWorkspace } from "@ui/providers/workspace";
import { useMaybeRecord } from "@ui/providers/record";
import { DbRecordFilter, DatabaseFieldDataType, RecordValues, DatabaseColumn, DatabaseColumnReturnValue, ReadOnlyColumns } from "@repo/app-db-utils/src/typings/db";
import { recordValueToText } from "@repo/app-db-utils/src/utils/db";
import { FormFieldBody } from "@ui/components/workspace/main/views/form/components/common/formFieldBody";
import { AccessLevel } from "@ui/typings/page";
import { usePage } from "@ui/providers/page";
import { Database } from "@ui/typings/database";

interface AddRecordModalProps {
    open: boolean;
    onClose: () => void;
    databaseId: string;
    viewFilter?: DbRecordFilter;
    contextualFilter?: DbRecordFilter;
    onRecordCreated?: (recordId: string) => void;
}

interface FilterAnalysis {
    prePopulatedValues: RecordValues;
    editableFields: DatabaseColumn[];
    conflictingFields: string[];
}

export const AddRecordModal = ({ 
    open, 
    onClose, 
    databaseId, 
    viewFilter, 
    contextualFilter,
    onRecordCreated 
}: AddRecordModalProps) => {
    const { createRecords } = useViews();
    const { databaseStore } = useWorkspace();
    const { accessLevel } = usePage();
    const maybeRecord = useMaybeRecord();
    
    const [recordValues, setRecordValues] = useState<RecordValues>({});
    const [isCreating, setIsCreating] = useState(false);
    const [analysis, setAnalysis] = useState<FilterAnalysis | null>(null);

    const database = databaseStore[databaseId];
    const canEdit = accessLevel && [AccessLevel.Full, AccessLevel.Edit].includes(accessLevel);

    useEffect(() => {
        if (!database || !open) return;

        const analysis = analyzeFilters(
            database.database,
            viewFilter,
            contextualFilter,
            maybeRecord?.recordInfo.record.id
        );

        setAnalysis(analysis);
        setRecordValues(analysis.prePopulatedValues);
    }, [database, viewFilter, contextualFilter, maybeRecord, open]);

    // Reset form when modal closes
    useEffect(() => {
        if (!open) {
            setRecordValues({});
        }
    }, [open]);

    const handleCreate = async () => {
        if (!canEdit || !database) return;

        setIsCreating(true);
        try {
            const result = await createRecords(databaseId, [recordValues]);
            if (result && result.records && result.records.length > 0) {
                onRecordCreated?.(result.records[0].id);
                onClose();
            }
        } catch (error) {
            console.error('Failed to create record:', error);
        } finally {
            setIsCreating(false);
        }
    };

    if (!database || !analysis) return null;

    // Get all editable fields that are not pre-populated
    const editableFieldsToShow = analysis.editableFields.filter(field =>
        !analysis.prePopulatedValues.hasOwnProperty(field.id)
    );

    const hasEditableFields = editableFieldsToShow.length > 0;
    const hasConflicts = analysis.conflictingFields.length > 0;
    const hasAnyContent = Object.keys(analysis.prePopulatedValues).length > 0 || hasEditableFields || hasConflicts;

    const updateValues = (update: RecordValues) => {
        setRecordValues(prev => ({...prev, ...update}));
    };

    return (
        <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="max-w-2xl max-h-[90vh] !rounded-none p-0" hideCloseBtn>
                <DialogHeader className="p-4 border-b flex flex-row items-center justify-between">
                    <div className="flex items-center gap-2">
                        <DialogTitle className="text-sm font-semibold">Add New Record</DialogTitle>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            className="text-xs rounded-full"
                            onClick={onClose}
                            disabled={isCreating}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleCreate}
                            size="sm"
                            className="text-xs rounded-full bg-black text-white hover:bg-gray-800"
                            disabled={isCreating || !canEdit}
                        >
                            {isCreating ? 'Creating...' : 'Create'}
                        </Button>
                    </div>
                </DialogHeader>

                <div className="max-h-[calc(90vh-120px)] overflow-auto mention-input-container">
                    <div className="p-4 space-y-6"> 
                        {(Object.keys(analysis.prePopulatedValues).length > 0 || hasConflicts) && (
                            <div className="text-sm text-gray-600">
                                {Object.keys(analysis.prePopulatedValues).length > 0 && (
                                    <div className="text-green-600 mb-1">
                                        Some fields have been pre-populated based on current filters.
                                    </div>
                                )}
                                {hasConflicts && (
                                    <div className="text-orange-600">
                                        Some filters couldn't be used for pre-population and will need manual input.
                                    </div>
                                )}
                            </div>
                        )}

                        {Object.keys(analysis.prePopulatedValues).length > 0 && (
                            <div className="border border-green-200 rounded-lg">
                                <div className="px-3 py-2 border-b border-green-200 bg-green-50">
                                    <h4 className="text-sm font-semibold text-green-700">Pre-populated Fields</h4>
                                </div>
                                <div className="p-3 space-y-3">
                                    {Object.entries(analysis.prePopulatedValues).map(([columnId, value]) => {
                                        const column = database.database.definition.columnsMap[columnId];
                                        if (!column) return null;

                                    //resolve option IDs to titles 
                                        const getDisplayValue = () => {
                                            if (column.type === DatabaseFieldDataType.Select && Array.isArray(value)) {
                                                const selectColumn = column as any;
                                                const stringValues = value as string[];
                                                return stringValues.map((optionId: string) => {
                                                    const option = selectColumn.optionsMap?.[optionId];
                                                    return option?.title || optionId;
                                                }).join(', ');
                                            }
                                            return recordValueToText(value as DatabaseColumnReturnValue);
                                        };

                                        return (
                                            <div key={columnId} className="flex justify-between items-center">
                                                <span className="text-sm font-medium text-gray-900">{column.title}</span>
                                                <span className="text-sm text-gray-600">
                                                    {getDisplayValue()}
                                                </span>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        )}

                        {hasEditableFields && (
                            <div className="border border-gray-200 rounded-lg">
                                <div className="px-3 py-2 border-b border-gray-200 bg-gray-50">
                                    <h4 className="text-sm font-semibold text-gray-900">Fields</h4>
                                </div>
                                <div className="p-3 space-y-4">
                                    {editableFieldsToShow.map((field) => (
                                        <div key={field.id}>
                                            <FormFieldBody
                                                id={field.id}
                                                values={recordValues}
                                                updateValues={updateValues}
                                                columnsMap={database.database.definition.columnsMap}
                                                columnsPropMap={{}}
                                                databaseId={databaseId}
                                                disabled={!canEdit}
                                                isEditing
                                                activeField={''}
                                                setActiveField={() => {}}
                                                updateFieldProps={() => {}}
                                            />
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {!hasAnyContent && (
                            <div className="border border-gray-200 rounded-lg">
                                <div className="p-6 text-center">
                                    <div className="text-gray-500 text-sm">
                                        No editable fields available for this view.
                                    </div>
                                    <div className="text-gray-400 text-xs mt-1">
                                        All fields in this view are either read-only or auto-generated.
                                    </div>
                                </div>
                            </div>
                        )}

                        {hasConflicts && (
                            <div className="border border-orange-200 rounded-lg">
                                <div className="px-3 py-2 border-b border-orange-200 bg-orange-50">
                                    <h4 className="text-sm font-semibold text-orange-700">Fields Requiring Input</h4>
                                </div>
                                <div className="p-3">
                                    <div className="text-sm text-gray-600">
                                        The following fields have filters that prevent pre-population: {analysis.conflictingFields.join(', ')}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};

function analyzeFilters(
    database: Database, 
    viewFilter?: DbRecordFilter, 
    contextualFilter?: DbRecordFilter,
    currentRecordId?: string
): FilterAnalysis {
    const prePopulatedValues: RecordValues = {};
    const editableFields: DatabaseColumn[] = [];
    const conflictingFields: string[] = [];

    // Get all editable fields (exclude read-only and auto-generated fields)
    Object.values(database.definition.columnsMap).forEach(column => {
        if (!ReadOnlyColumns.includes(column.type)) {
            editableFields.push(column);
        }
    });

    if (viewFilter?.conditions) {
        analyzeFilterConditions(
            viewFilter.conditions, 
            database, 
            prePopulatedValues, 
            conflictingFields,
            currentRecordId
        );
    }

    if (contextualFilter?.conditions) {
        analyzeFilterConditions(
            contextualFilter.conditions, 
            database, 
            prePopulatedValues, 
            conflictingFields,
            currentRecordId
        );
    }

    return {
        prePopulatedValues,
        editableFields,
        conflictingFields: Array.from(new Set(conflictingFields))
    };
}

function analyzeFilterConditions(
    conditions: any[], 
    database: Database, 
    prePopulatedValues: RecordValues, 
    conflictingFields: string[],
    currentRecordId?: string
) {
    conditions.forEach(condition => {
        const column = database.definition.columnsMap[condition.columnId];
        if (!column) return;

        if (prePopulatedValues.hasOwnProperty(condition.columnId)) return;

        if (condition.op === 'equals' || condition.op === 'is') {
            let value = condition.value;
            
            if (value === 'current_record' && currentRecordId) {
                value = currentRecordId;
            } else if (Array.isArray(value) && value.includes('current_record') && currentRecordId) {
                value = value.map(v => v === 'current_record' ? currentRecordId : v);
            }

            if (value && value !== 'current_record') {
                prePopulatedValues[condition.columnId] = value;
            }
        } else {
            conflictingFields.push(column.title);
        }
    });
}

 